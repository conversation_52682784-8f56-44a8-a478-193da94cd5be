# Quản lý Tồn kho Sản phẩm User

## Tổ<PERSON> quan

Hệ thống đã đư<PERSON><PERSON> bổ sung tính năng quản lý tồn kho cho sản phẩm của người dùng. Người dùng có thể:

- <PERSON>em thông tin tồn kho của sản phẩm theo kho
- Tạo/cập nhật thông tin tồn kho cho sản phẩm
- Chọn kho để quản lý tồn kho
- Quản lý số lượng chi tiết (sẵn sàng, giữ chỗ, lỗi)

## API Endpoints

### 1. L<PERSON>y danh sách kho vật lý

**GET** `/api/v1/user/products/warehouses`

Lấy danh sách tất cả các kho vật lý có thể sử dụng để quản lý tồn kho.

**Response:**
```json
{
  "success": true,
  "message": "<PERSON><PERSON><PERSON> danh sách kho thành công",
  "data": [
    {
      "warehouseId": 1,
      "name": "<PERSON><PERSON> ch<PERSON>h",
      "description": "<PERSON>ho chính tại Hà Nội",
      "type": "PHYSICAL",
      "address": "123 Đường ABC, Hà Nội",
      "capacity": 5000
    },
    {
      "warehouseId": 2,
      "name": "Kho phụ",
      "description": "Kho phụ tại TP.HCM",
      "type": "PHYSICAL",
      "address": "456 Đường XYZ, TP.HCM",
      "capacity": 3000
    }
  ]
}
```

### 2. Lấy thông tin tồn kho của sản phẩm

**GET** `/api/v1/user/products/{productId}/inventory?warehouseId={warehouseId}`

Lấy thông tin tồn kho của sản phẩm. Nếu không chỉ định `warehouseId`, sẽ trả về tồn kho của tất cả các kho.

**Parameters:**
- `productId` (path): ID sản phẩm
- `warehouseId` (query, optional): ID kho

**Response:**
```json
{
  "success": true,
  "message": "Lấy thông tin tồn kho thành công",
  "data": [
    {
      "id": 1,
      "productId": 123,
      "warehouseId": 1,
      "currentQuantity": 115,
      "totalQuantity": 115,
      "availableQuantity": 100,
      "reservedQuantity": 10,
      "defectiveQuantity": 5,
      "lastUpdated": 1703123456789,
      "warehouse": {
        "warehouseId": 1,
        "name": "Kho chính",
        "description": "Kho chính tại Hà Nội",
        "type": "PHYSICAL",
        "metadata": {}
      }
    }
  ]
}
```

### 3. Tạo/cập nhật tồn kho cho sản phẩm

**POST** `/api/v1/user/products/{productId}/inventory`

Tạo mới hoặc cập nhật thông tin tồn kho cho sản phẩm trong kho cụ thể.

**Parameters:**
- `productId` (path): ID sản phẩm

**Request Body:**
```json
{
  "warehouseId": 1,
  "availableQuantity": 100,
  "reservedQuantity": 10,
  "defectiveQuantity": 5
}
```

**Response:**
```json
{
  "success": true,
  "message": "Tạo/cập nhật tồn kho thành công",
  "data": {
    "id": 1,
    "productId": 123,
    "warehouseId": 1,
    "currentQuantity": 115,
    "totalQuantity": 115,
    "availableQuantity": 100,
    "reservedQuantity": 10,
    "defectiveQuantity": 5,
    "lastUpdated": 1703123456789,
    "warehouse": {
      "warehouseId": 1,
      "name": "Kho chính",
      "description": "Kho chính tại Hà Nội",
      "type": "PHYSICAL",
      "metadata": {}
    }
  }
}
```

## Cấu trúc dữ liệu

### ProductInventoryDto

```typescript
{
  warehouseId?: number;        // ID kho (optional)
  availableQuantity?: number;  // Số lượng sẵn sàng (>= 0)
  reservedQuantity?: number;   // Số lượng giữ chỗ (>= 0)
  defectiveQuantity?: number;  // Số lượng lỗi (>= 0)
}
```

### InventoryResponseDto

```typescript
{
  id: number;                  // ID bản ghi tồn kho
  productId: number;           // ID sản phẩm
  warehouseId: number;         // ID kho
  currentQuantity: number;     // Số lượng hiện tại (tự động tính)
  totalQuantity: number;       // Tổng số lượng (tự động tính)
  availableQuantity: number;   // Số lượng sẵn sàng
  reservedQuantity: number;    // Số lượng giữ chỗ
  defectiveQuantity: number;   // Số lượng lỗi
  lastUpdated: number;         // Thời gian cập nhật cuối
  warehouse?: {                // Thông tin kho
    warehouseId: number;
    name: string;
    description: string;
    type: string;
    metadata: any;
  };
}
```

## Quy tắc tính toán

- **currentQuantity** = availableQuantity + reservedQuantity + defectiveQuantity
- **totalQuantity** = currentQuantity (có thể mở rộng để tính lịch sử nhập/xuất)

## Lưu ý

1. Chỉ có thể quản lý tồn kho của sản phẩm thuộc về mình
2. Kho phải tồn tại trong hệ thống
3. Tất cả số lượng phải >= 0
4. Nếu đã có tồn kho cho sản phẩm và kho, sẽ cập nhật. Nếu chưa có, sẽ tạo mới
5. Thời gian cập nhật được tự động ghi nhận
