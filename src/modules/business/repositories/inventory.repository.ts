import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Inventory, Warehouse } from '@modules/business/entities';
import { PaginatedResult } from '@common/response';


/**
 * Repository xử lý các thao tác với bảng inventory
 */
@Injectable()
export class InventoryRepository extends Repository<Inventory> {
  private readonly logger = new Logger(InventoryRepository.name);

  constructor(private dataSource: DataSource) {
    super(Inventory, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho inventory
   * @returns Query builder
   */
  private createBaseQuery(): SelectQueryBuilder<Inventory> {
    return this.createQueryBuilder('inventory');
  }

  /**
   * Tìm bản ghi tồn kho theo ID
   * @param id ID của bản ghi tồn kho
   * @returns Thông tin tồn kho hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<Inventory | null> {
    try {
      return await this.createBaseQuery()
        .where('inventory.id = :id', { id })
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm tồn kho theo ID ${id}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm tồn kho theo ID ${id}: ${error.message}`);
    }
  }

  /**
   * Tìm bản ghi tồn kho theo ID và kết hợp với thông tin kho
   * @param id ID của bản ghi tồn kho
   * @returns Thông tin tồn kho với thông tin kho đầy đủ hoặc null nếu không tìm thấy
   */
  async findByIdWithWarehouseDetails(id: number): Promise<any | null> {
    try {
      // Tìm thông tin tồn kho
      const inventory = await this.findById(id);
      if (!inventory || !inventory.warehouseId) {
        return inventory;
      }

      // Lấy thông tin kho
      const warehouse = await this.dataSource
        .createQueryBuilder()
        .select('w.*')
        .from(Warehouse, 'w')
        .where('w.warehouseId = :warehouseId', { warehouseId: inventory.warehouseId })
        .getRawOne();

      if (!warehouse) {
        return inventory;
      }

      // Lấy danh sách trường tùy chỉnh của kho - Logic đã bị xóa
      // WarehouseCustomField đã bị xóa hoàn toàn
      /*
      const customFields = await this.dataSource
        .createQueryBuilder()
        .select('wcf.*')
        .from(WarehouseCustomField, 'wcf')
        .where('wcf.warehouseId = :warehouseId', { warehouseId: inventory.warehouseId })
        .getRawMany();
      */
      const customFields = []; // Thay thế bằng mảng rỗng

      // Kết hợp thông tin
      return {
        ...inventory,
        warehouse: {
          ...warehouse,
          customFields: customFields || [],
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tìm tồn kho với thông tin kho theo ID ${id}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm tồn kho với thông tin kho theo ID ${id}: ${error.message}`);
    }
  }

  /**
   * Tìm bản ghi tồn kho theo ID sản phẩm và ID kho
   * @param productId ID của sản phẩm
   * @param warehouseId ID của kho
   * @returns Thông tin tồn kho hoặc null nếu không tìm thấy
   */
  async findByProductAndWarehouse(productId: number, warehouseId: number): Promise<Inventory | null> {
    try {
      return await this.createBaseQuery()
        .where('inventory.productId = :productId', { productId })
        .andWhere('inventory.warehouseId = :warehouseId', { warehouseId })
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm tồn kho theo ID sản phẩm ${productId} và ID kho ${warehouseId}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm tồn kho theo ID sản phẩm ${productId} và ID kho ${warehouseId}: ${error.message}`);
    }
  }

  /**
   * Tìm bản ghi tồn kho theo ID sản phẩm và ID kho (cho phép warehouseId null)
   * @param productId ID của sản phẩm
   * @param warehouseId ID của kho (có thể null)
   * @returns Thông tin tồn kho hoặc null nếu không tìm thấy
   */
  async findByProductAndWarehouseNullable(productId: number, warehouseId: number | null): Promise<Inventory | null> {
    try {
      const queryBuilder = this.createBaseQuery()
        .where('inventory.productId = :productId', { productId });

      if (warehouseId === null) {
        queryBuilder.andWhere('inventory.warehouseId IS NULL');
      } else {
        queryBuilder.andWhere('inventory.warehouseId = :warehouseId', { warehouseId });
      }

      return await queryBuilder.getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm tồn kho theo ID sản phẩm ${productId} và ID kho ${warehouseId}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm tồn kho theo ID sản phẩm ${productId} và ID kho ${warehouseId}: ${error.message}`);
    }
  }

  /**
   * Tìm kiếm tồn kho với các điều kiện lọc và phân trang
   * @param queryParams Tham số truy vấn
   * @returns Danh sách tồn kho với phân trang
   */
  async findAll(queryParams: any): Promise<PaginatedResult<Inventory>> {
    try {
      const {
        page = 1,
        limit = 10,
        offset = (page - 1) * limit,
        productId,
        warehouseId,
        sortBy = 'id',
        sortDirection = 'ASC',
      } = queryParams;

      // Tạo query builder
      const queryBuilder = this.createBaseQuery();

      // Thêm điều kiện lọc theo ID sản phẩm nếu có
      if (productId) {
        queryBuilder.andWhere('inventory.productId = :productId', { productId });
      }

      // Thêm điều kiện lọc theo ID kho nếu có
      if (warehouseId) {
        queryBuilder.andWhere('inventory.warehouseId = :warehouseId', { warehouseId });
      }

      // Đếm tổng số bản ghi
      const total = await queryBuilder.getCount();

      // Thêm sắp xếp và phân trang
      queryBuilder
        .orderBy(`inventory.${sortBy}`, sortDirection as 'ASC' | 'DESC')
        .skip(offset)
        .take(limit);

      // Lấy danh sách tồn kho
      const items = await queryBuilder.getMany();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tìm kiếm tồn kho: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm kiếm tồn kho: ${error.message}`);
    }
  }

  /**
   * Tạo mới bản ghi tồn kho
   * @param inventory Thông tin tồn kho cần tạo
   * @returns Bản ghi tồn kho đã tạo
   */
  async createInventory(inventory: Inventory): Promise<Inventory> {
    try {
      return await this.save(inventory);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo tồn kho: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tạo tồn kho: ${error.message}`);
    }
  }

  /**
   * Cập nhật bản ghi tồn kho
   * @param id ID của bản ghi tồn kho
   * @param updateData Dữ liệu cập nhật
   * @returns Bản ghi tồn kho đã cập nhật
   */
  async updateInventory(id: number, updateData: Partial<Inventory>): Promise<Inventory> {
    try {
      await this.update(id, updateData);
      const updatedInventory = await this.findById(id);
      if (!updatedInventory) {
        throw new Error(`Không tìm thấy tồn kho với ID ${id} sau khi cập nhật`);
      }
      return updatedInventory;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật tồn kho ${id}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi cập nhật tồn kho ${id}: ${error.message}`);
    }
  }

  /**
   * Xóa bản ghi tồn kho
   * @param id ID của bản ghi tồn kho
   * @returns Kết quả xóa
   */
  async deleteInventory(id: number): Promise<boolean> {
    try {
      const result = await this.delete(id);
      return result && result.affected ? result.affected > 0 : false;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa tồn kho ${id}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi xóa tồn kho ${id}: ${error.message}`);
    }
  }
}
