import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, ParseIntPipe, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { UserProductService } from '@modules/business/user/services';
import { BusinessCreateProductDto, BusinessBatchCreateProductDto, BatchProductResponseDto, BusinessPresignedUrlImageDto, BusinessUpdateProductDto, BusinessUploadUrlsDto, ProductResponseDto, QueryProductDto, BulkDeleteProductDto, BulkDeleteProductResponseDto } from '../dto';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import { SWAGGER_API_TAGS } from '@common/swagger';

/**
 * Controller x<PERSON> lý các request liên quan đến sản phẩm của người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_BUSINESS)
@Controller('user/products')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(ApiResponseDto, ProductResponseDto, BusinessPresignedUrlImageDto, BusinessUploadUrlsDto, BusinessCreateProductDto, BusinessUpdateProductDto, BulkDeleteProductDto, BulkDeleteProductResponseDto)
export class UserProductController {
  constructor(private readonly userProductService: UserProductService) {}

  /**
   * Tạo sản phẩm mới
   * @param createProductDto DTO chứa thông tin sản phẩm mới
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã tạo
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Tạo sản phẩm mới' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Sản phẩm đã được tạo thành công',
    type: () => ApiResponseDto.getSchema(ProductResponseDto),
  })
  async createProduct(
    @Body() createProductDto: BusinessCreateProductDto,
    @CurrentUser('id') userId: number,
  ) {
    const product = await this.userProductService.createProduct(createProductDto, userId);
    return ApiResponseDto.created<ProductResponseDto>(product, 'Tạo sản phẩm thành công');
  }

  /**
   * Tạo nhiều sản phẩm cùng lúc
   * @param batchCreateDto DTO chứa danh sách sản phẩm cần tạo
   * @param userId ID của người dùng hiện tại
   * @returns Kết quả tạo batch với thông tin thành công và thất bại
   */
  @Post('batch')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Tạo nhiều sản phẩm cùng lúc',
    description: 'Tạo nhiều sản phẩm cùng lúc. API sẽ xử lý từng sản phẩm một cách tuần tự và trả về kết quả chi tiết cho từng sản phẩm.'
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Kết quả tạo batch sản phẩm',
    type: () => ApiResponseDto.getSchema(BatchProductResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  async batchCreateProducts(
    @Body() batchCreateDto: BusinessBatchCreateProductDto,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.userProductService.batchCreateProducts(batchCreateDto, userId);

    // Nếu tất cả sản phẩm đều tạo thành công
    if (result.failedCount === 0) {
      return ApiResponseDto.created<BatchProductResponseDto>(result, 'Tạo tất cả sản phẩm thành công');
    }

    // Nếu có một số sản phẩm thất bại
    if (result.successCount > 0) {
      return ApiResponseDto.success<BatchProductResponseDto>(result, `Tạo thành công ${result.successCount}/${result.totalProducts} sản phẩm`);
    }

    // Nếu tất cả sản phẩm đều thất bại
    return ApiResponseDto.success<BatchProductResponseDto>(result, 'Không có sản phẩm nào được tạo thành công');
  }

  /**
   * Lấy danh sách sản phẩm
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách sản phẩm với phân trang
   */
  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Lấy danh sách sản phẩm' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách sản phẩm',
    type: () => ApiResponseDto.getPaginatedSchema(ProductResponseDto),
  })
  async getProducts(@Query() queryDto: QueryProductDto) {
    const products = await this.userProductService.getProducts(queryDto);
    return ApiResponseDto.success<PaginatedResult<ProductResponseDto>>(products, 'Lấy danh sách sản phẩm thành công');
  }

  /**
   * Lấy chi tiết sản phẩm theo ID
   * @param id ID của sản phẩm
   * @returns Chi tiết sản phẩm
   */
  @Get(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Lấy chi tiết sản phẩm' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Chi tiết sản phẩm',
    type: () => ApiResponseDto.getSchema(ProductResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sản phẩm',
  })
  async getProductDetail(@Param('id', ParseIntPipe) id: number) {
    const product = await this.userProductService.getProductDetail(id);
    return ApiResponseDto.success<ProductResponseDto>(product, 'Lấy chi tiết sản phẩm thành công');
  }

  /**
   * Cập nhật sản phẩm
   * @param id ID của sản phẩm cần cập nhật
   * @param updateProductDto DTO chứa thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã cập nhật
   */
  @Put(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Cập nhật sản phẩm' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Sản phẩm đã được cập nhật thành công',
    type: () => ApiResponseDto.getSchema(ProductResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sản phẩm',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu không hợp lệ',
  })
  async updateProduct(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateProductDto: BusinessUpdateProductDto,
    @CurrentUser('id') userId: number,
  ) {
    const product = await this.userProductService.updateProduct(id, updateProductDto, userId);
    return ApiResponseDto.success<ProductResponseDto>(product, 'Cập nhật sản phẩm thành công');
  }

  /**
   * Xóa nhiều sản phẩm (soft delete)
   * @param bulkDeleteDto DTO chứa danh sách ID sản phẩm cần xóa
   * @param userId ID của người dùng hiện tại
   * @returns Kết quả xóa nhiều sản phẩm
   */
  @Delete('bulk')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Xóa nhiều sản phẩm' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa nhiều sản phẩm thành công',
    type: () => ApiResponseDto.getSchema(BulkDeleteProductResponseDto),
  })
  @ApiResponse({
    status: 207,
    description: 'Một số sản phẩm không thể xóa',
    type: () => ApiResponseDto.getSchema(BulkDeleteProductResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  async bulkDeleteProducts(
    @Body() bulkDeleteDto: BulkDeleteProductDto,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.userProductService.bulkDeleteProducts(bulkDeleteDto, userId);

    return ApiResponseDto.success(result, result.message);
  }

  /**
   * Xóa sản phẩm (soft delete)
   * @param id ID của sản phẩm cần xóa
   * @param userId ID của người dùng hiện tại
   * @returns Thông báo xóa thành công
   */
  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Xóa sản phẩm' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Sản phẩm đã được xóa thành công',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sản phẩm',
  })
  async deleteProduct(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ) {
    await this.userProductService.deleteProduct(id, userId);
    return ApiResponseDto.success(null, 'Sản phẩm đã được xóa thành công');
  }
}
