# 🗑️ Bulk Delete Physical Warehouse API

## 🎯 Tổng quan

API này cho phép xóa nhiều kho vật lý cùng lúc trong một request duy nhất, giú<PERSON> tối ưu hóa hiệu suất và giảm s<PERSON> lượ<PERSON> request cần thiết.

## 🔗 Endpoint

```
DELETE /v1/user/physical-warehouses/bulk
```

## 🔐 Authentication

Yêu cầu JWT token trong header:
```
Authorization: Bearer <your-jwt-token>
```

## 📥 Request Body

### Schema

```typescript
{
  warehouseIds: number[]  // Mảng ID kho vật lý cần xóa (1-100 items)
}
```

### Ví dụ Request

```json
{
  "warehouseIds": [1, 2, 3, 4, 5]
}
```

## 📤 Response

### Thành công hoàn toàn (200 OK)

```json
{
  "success": true,
  "message": "<PERSON>óa thành công 5/5 kho vật lý",
  "data": {
    "totalRequested": 5,
    "successCount": 5,
    "failureCount": 0,
    "results": [
      {
        "warehouseId": 1,
        "status": "success",
        "message": "Xóa kho vật lý thành công"
      },
      {
        "warehouseId": 2,
        "status": "success",
        "message": "Xóa kho vật lý thành công"
      },
      {
        "warehouseId": 3,
        "status": "success",
        "message": "Xóa kho vật lý thành công"
      },
      {
        "warehouseId": 4,
        "status": "success",
        "message": "Xóa kho vật lý thành công"
      },
      {
        "warehouseId": 5,
        "status": "success",
        "message": "Xóa kho vật lý thành công"
      }
    ],
    "message": "Xóa thành công 5/5 kho vật lý"
  }
}
```

### Thành công một phần (207 Multi-Status)

```json
{
  "success": true,
  "message": "Xóa thành công 3/5 kho vật lý",
  "data": {
    "totalRequested": 5,
    "successCount": 3,
    "failureCount": 2,
    "results": [
      {
        "warehouseId": 1,
        "status": "success",
        "message": "Xóa kho vật lý thành công"
      },
      {
        "warehouseId": 2,
        "status": "error",
        "message": "Kho với ID 2 không phải là kho vật lý"
      },
      {
        "warehouseId": 3,
        "status": "success",
        "message": "Xóa kho vật lý thành công"
      },
      {
        "warehouseId": 4,
        "status": "error",
        "message": "Không tìm thấy thông tin kho vật lý với ID 4"
      },
      {
        "warehouseId": 5,
        "status": "success",
        "message": "Xóa kho vật lý thành công"
      }
    ],
    "message": "Xóa thành công 3/5 kho vật lý"
  }
}
```

## ❌ Error Responses

### 400 Bad Request - Dữ liệu không hợp lệ

```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "field": "warehouseIds",
      "message": "Phải có ít nhất một kho để xóa"
    }
  ]
}
```

### 500 Internal Server Error

```json
{
  "success": false,
  "message": "Lỗi khi xóa bulk kho vật lý",
  "error": {
    "code": 30002,
    "message": "Lỗi khi xóa kho"
  }
}
```

## 📋 Validation Rules

- `warehouseIds`: Bắt buộc, mảng số nguyên, ít nhất 1 phần tử, không trùng lặp
- Mỗi `warehouseId` phải là số nguyên dương
- Tối đa 100 kho trong một request

## 🔍 Business Logic

1. **Kiểm tra từng kho:**
   - Kho có tồn tại không
   - Kho có phải là kho vật lý không
   - Người dùng có quyền xóa kho không

2. **Xóa theo thứ tự:**
   - Xóa thông tin kho vật lý từ bảng `physical_warehouse`
   - Xóa thông tin kho chung từ bảng `warehouse`

3. **Transaction:**
   - Toàn bộ quá trình được thực hiện trong transaction
   - Nếu có lỗi nghiêm trọng, rollback toàn bộ

## 📊 Status Codes

- **200 OK**: Tất cả kho được xóa thành công
- **207 Multi-Status**: Một số kho được xóa thành công, một số thất bại
- **400 Bad Request**: Dữ liệu đầu vào không hợp lệ
- **401 Unauthorized**: Không có token hoặc token không hợp lệ
- **500 Internal Server Error**: Lỗi server

## 🧪 Ví dụ sử dụng

### cURL

```bash
curl -X DELETE "http://localhost:3000/v1/user/physical-warehouses/bulk" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "warehouseIds": [1, 2, 3]
  }'
```

### JavaScript/Fetch

```javascript
const response = await fetch('/v1/user/physical-warehouses/bulk', {
  method: 'DELETE',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    warehouseIds: [1, 2, 3]
  })
});

const result = await response.json();
console.log(`Deleted ${result.data.successCount}/${result.data.totalRequested} warehouses`);
```

### Axios

```javascript
try {
  const response = await axios.delete('/v1/user/physical-warehouses/bulk', {
    data: { warehouseIds: [1, 2, 3] },
    headers: { Authorization: `Bearer ${token}` }
  });
  
  console.log('Bulk delete successful:', response.data);
} catch (error) {
  if (error.response?.status === 207) {
    // Partial success
    console.log('Partial success:', error.response.data);
  } else {
    console.error('Bulk delete failed:', error.response?.data);
  }
}
```

## ⚠️ Lưu ý quan trọng

1. **Không thể hoàn tác**: Sau khi xóa, dữ liệu không thể khôi phục
2. **Kiểm tra quyền**: Chỉ có thể xóa kho thuộc về người dùng hiện tại
3. **Giới hạn số lượng**: Tối đa 100 kho trong một request
4. **Performance**: Với số lượng lớn, nên chia thành nhiều batch nhỏ
