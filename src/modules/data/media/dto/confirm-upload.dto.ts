import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO cho việc confirm upload thành công
 */
export class ConfirmUploadDto {
  @ApiProperty({
    description: 'Danh sách storage keys đã upload thành công',
    example: [
      'media/IMAGE/user_1/2024/01/15/My_beautiful_image.jpg',
      'media/VIDEO/user_1/2024/01/15/My_video.mp4'
    ],
    type: [String],
  })
  @IsArray({ message: 'Storage keys phải là một mảng' })
  @IsNotEmpty({ message: 'Danh sách storage keys không được để trống' })
  @IsString({ each: true, message: 'Mỗi storage key phải là chuỗi' })
  storageKeys: string[];
}

/**
 * DTO cho response của confirm upload
 */
export class ConfirmUploadResponseDto {
  @ApiProperty({
    description: 'Danh sách ID của media đã được cập nhật thành công',
    example: ['uuid-1', 'uuid-2'],
    type: [String],
  })
  updatedIds: string[];

  @ApiProperty({
    description: 'Danh sách storage keys bị bỏ qua (không tìm thấy hoặc lỗi)',
    example: ['media/IMAGE/user_1/2024/01/15/not_found.jpg'],
    type: [String],
  })
  skippedKeys: string[];

  @ApiProperty({
    description: 'Tổng số media đã được cập nhật',
    example: 2,
  })
  totalUpdated: number;

  @ApiProperty({
    description: 'Tổng số storage keys bị bỏ qua',
    example: 1,
  })
  totalSkipped: number;
}
