import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsNumber, IsArray, IsO<PERSON>al, IsPositive } from 'class-validator';
import {Media} from "@modules/data/media/entities";
import { IsAllowedMediaType } from '../validators/media-type.validator';


export class MediaResponseDto {
    @ApiProperty({
        description: 'ID của media',
        example: '123e4567-e89b-12d3-a456-426614174000'
    })
    @IsString()
    @IsNotEmpty()
    id: string;

    @ApiProperty({
        description: 'Tên file media',
        example: 'My beautiful image'
    })
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiProperty({
        description: 'Mô tả về media',
        example: 'An image uploaded by user'
    })
    description: string;

    @ApiProperty({
        description: 'Mảng tag phân loại media (ví dụ: ["shoe", "white sneaker"])',
        example: ['shoe', 'white sneaker']
    })
    @IsArray()
    @IsString({ each: true })
    @IsNotEmpty({ each: true })
    tags: string[];

    @ApiProperty({
        description: 'URL tải xuống media',
        example: 'https://example.com/media/123e4567-e89b-12d3-a456-426614174000.jpg'
    })
    @IsString()
    @IsNotEmpty()
    downloadURL: string;

    @ApiProperty({
        description: 'URL xem media',
        example: 'https://example.com/media/123e4567-e89b-12d3-a456-426614174000.jpg'
    })
    @IsString()
    @IsNotEmpty()
    viewURL: string;

    @ApiProperty({
        description: 'Tên người đăng tải',
        example: 'Nguyễn Văn A'
    })
    @IsString()
    @IsNotEmpty()
    author?: string;

    @ApiProperty({
        description: 'Avatar của người đăng tải',
        example: 'https://example.com/avatar/123e4567-e89b-12d3-a456-426614174000.jpg'
    })
    @IsString()
    @IsNotEmpty()
    avatar?: string;

}

 /// DTO media cho user
export class MediaDto {
    @ApiProperty({
      description: 'Tên file media',
      example: 'My beautiful image'
    })
    @IsString()
    @IsNotEmpty()
    name: string;
  
    @ApiProperty({
      description: 'Mô tả về media',
      example: 'An image uploaded by user'
    })
    @IsString()
    @IsNotEmpty()
    description: string;
  
    @ApiProperty({
      description: 'Dung lượng file (byte)',
      example: 1048576
    })
    @IsNumber()
    @IsNotEmpty()
    @IsPositive()
    size: number;
  
    @ApiProperty({
      description: 'Mảng tag phân loại media (ví dụ: ["shoe", "white sneaker"])',
      example: ['shoe', 'white sneaker']
    })
    @IsArray()
    @IsString({ each: true })
    @IsNotEmpty({ each: true })
    tags: string[];
  
    @ApiProperty({
      description: 'Type phân loại media (ví dụ: "image/png", "video/mp4", "audio/mp3") - Chỉ hỗ trợ image, video, audio',
      example: 'image/png'
    })
    @IsString()
    @IsNotEmpty()
    @IsAllowedMediaType({ message: 'Loại media không được hỗ trợ. Chỉ hỗ trợ image/, video/, audio/' })
    type: string;
  
  
    @IsString()
    @IsNotEmpty()
    @ApiProperty({ example: 'test/test-1744857340088-abc123' })
    viewUrl: string;
  }
  
  
  