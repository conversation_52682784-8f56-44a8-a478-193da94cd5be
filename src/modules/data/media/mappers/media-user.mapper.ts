import { CdnService } from '@/shared/services/cdn.service';
import { S3Service } from '@shared/services/s3.service';
import { UserRepository } from "@/modules/user/repositories/user.repository";
import { AdminMediaResponseDto } from '../dto/media-admin.dto';
import { plainToInstance } from "class-transformer";
import { TimeIntervalEnum } from '@/shared/utils';
import axios from 'axios';
import { Media } from '../entities';
import { MediaDto } from '../dto/media-user.dto';

export class MediaUserMapper {
    static async toUserDto(
        media: Media,
        cdnService: CdnService,
    ): Promise<MediaDto> {
        const dto = plainToInstance(MediaDto, media);
        if (media.storageKey) {
            try {
                // Gọi S3 để lấy download URL
                const downloadUrl = await cdnService.generateUrlView(media.storageKey, TimeIntervalEnum.ONE_HOUR); // Thay đổi thời gian hết hạn nếu cần
                if (downloadUrl != null) {
                    dto.viewUrl = downloadUrl; // Gán URL vào storageKey
                } else {
                    console.warn(`CDN generateUrlView returned null for storageKey: ${media.storageKey}`);
                    dto.viewUrl = '';
                }
            } catch (e) {
                console.error(`Lỗi tạo URL cho ${media.storageKey}:`, {
                    error: e.message,
                    stack: e.stack,
                    storageKey: media.storageKey,
                    mediaId: media.id,
                    mediaStatus: media.status
                });
                dto.viewUrl = ''; // Nếu không thể lấy URL thì gán là null
            }
        }
        return dto;
        
    }

    static async toUserList(mediaList: Media[], cdnService: CdnService): Promise<MediaDto[]> {
        
            // Sử dụng Promise.all để chạy đồng thời các yêu cầu lấy URL
            const mediaListWithUrl = await Promise.all(
                mediaList.map(async (media) => {
                    const dto = plainToInstance(MediaDto, media);
                    if (media.storageKey) {
                        try {
                            // Gọi S3 để lấy download URL
                            const downloadUrl = await cdnService.generateUrlView(media.storageKey, TimeIntervalEnum.ONE_HOUR); // Thay đổi thời gian hết hạn nếu cần
                            if (downloadUrl != null) {
                                dto.viewUrl = downloadUrl; // Gán URL vào storageKey
                            } else {
                                console.warn(`CDN generateUrlView returned null for storageKey: ${media.storageKey}`);
                                dto.viewUrl = '';
                            }
                        } catch (e) {
                            console.error(`Lỗi tạo URL cho ${media.storageKey}:`, {
                                error: e.message,
                                stack: e.stack,
                                storageKey: media.storageKey,
                                mediaId: media.id,
                                mediaStatus: media.status
                            });
                            dto.viewUrl = ''; // Nếu không thể lấy URL thì gán là null
                        }
                    }
                    return dto;
                }),
            );
            return mediaListWithUrl;
        
        
    }

}