import { FileTypeEnum, MarketplaceMediaTypeEnum } from '@shared/utils';
import { ImageTypeEnum } from '@shared/utils';
import { VideoTypeEnum } from '@shared/utils';
import { AudioTypeEnum } from '@shared/utils';
import { ContentMediaTypeEnum } from '@utils/file/content-media-type.util';



/**
 * Union type cho tất cả các loại media type được hỗ trợ
 * Bao gồm tất cả các loại để tương thích với các module khác
 */
export type MediaType = ImageTypeEnum | FileTypeEnum | VideoTypeEnum | AudioTypeEnum | MarketplaceMediaTypeEnum | ContentMediaTypeEnum;

/**
 * Object tiện ích để làm việc với tất cả các loại media type
 */
export const MediaTypeUtil = {
  /**
   * Lấy giá trị MIME type từ bất kỳ loại media type nào
   * @param type Loại media (ImageTypeEnum, VideoTypeEnum, AudioTypeEnum, FileTypeEnum, etc.)
   * @returns Giá trị MIME type tương ứng
   */
  getValue(type: MediaType): string {
    return type;
  },

  /**
   * Kiểm tra xem một giá trị có phải là ImageTypeEnum không
   * @param type Giá trị cần kiểm tra
   * @returns true nếu là ImageTypeEnum, false nếu không phải
   */
  isImageType(type: MediaType): type is ImageTypeEnum {
    return Object.values(ImageTypeEnum).includes(type as ImageTypeEnum);
  },

  /**
   * Kiểm tra xem một giá trị có phải là FileTypeEnum không
   * @param type Giá trị cần kiểm tra
   * @returns true nếu là FileTypeEnum, false nếu không phải
   */
  isFileType(type: MediaType): type is FileTypeEnum {
    return Object.values(FileTypeEnum).includes(type as FileTypeEnum);
  },

  /**
   * Kiểm tra xem một giá trị có phải là VideoTypeEnum không
   * @param type Giá trị cần kiểm tra
   * @returns true nếu là VideoTypeEnum, false nếu không phải
   */
  isVideoType(type: MediaType): type is VideoTypeEnum {
    return Object.values(VideoTypeEnum).includes(type as VideoTypeEnum);
  },

  /**
   * Kiểm tra xem một giá trị có phải là AudioTypeEnum không
   * @param type Giá trị cần kiểm tra
   * @returns true nếu là AudioTypeEnum, false nếu không phải
   */
  isAudioType(type: MediaType): type is AudioTypeEnum {
    return Object.values(AudioTypeEnum).includes(type as AudioTypeEnum);
  },

  /**
   * Kiểm tra xem một MIME type có được phép trong Media module không
   * Chỉ cho phép image, video và audio - không cho phép document
   * @param mimeType MIME type cần kiểm tra
   * @returns true nếu được phép, false nếu không
   */
  isAllowedInMediaModule(mimeType: string): boolean {
    return mimeType.startsWith('image/') ||
           mimeType.startsWith('video/') ||
           mimeType.startsWith('audio/');
  },
};
