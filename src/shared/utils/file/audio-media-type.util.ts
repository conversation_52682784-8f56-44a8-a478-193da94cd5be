import { AppException, ErrorCode } from '@common/exceptions/app.exception';

/**
 * Enum định nghĩa các loại MIME cho audio
 */
export enum AudioTypeEnum {
  MP3 = 'audio/mpeg',
  WAV = 'audio/wav',
  OGG = 'audio/ogg',
  AAC = 'audio/aac',
  FLAC = 'audio/flac',
  M4A = 'audio/mp4',
  WMA = 'audio/x-ms-wma',
  WEBM_AUDIO = 'audio/webm',
}

/**
 * Object tiện ích để làm việc với AudioTypeEnum
 */
export const AudioType = {
  /**
   * Lấy giá trị chuỗi của một loại audio
   * @param type Loại audio
   * @returns Giá trị MIME tương ứng
   */
  getValue(type: AudioTypeEnum): string {
    return type;
  },

  /**
   * Lấy enum AudioTypeEnum từ tên loại audio hoặc giá trị MIME type
   * @param type Tên loại audio (key của enum) hoặc giá trị MIME type (ví dụ: 'audio/mpeg')
   * @returns Giá trị enum AudioTypeEnum tương ứng
   * @throws AppException nếu loại audio không tồn tại
   */
  getType(type: string): AudioTypeEnum {
    // Kiểm tra nếu là giá trị MIME type (ví dụ: 'audio/mpeg')
    const entries = Object.entries(AudioTypeEnum);
    const entry = entries.find(([_, value]) => value === type);

    if (entry) {
      return AudioTypeEnum[entry[0] as keyof typeof AudioTypeEnum];
    }

    // Nếu không tìm thấy, ném exception
    throw new AppException(
      ErrorCode.FILE_TYPE_NOT_FOUND,
      `Loại audio '${type}' không được hỗ trợ`
    );
  },
};
